require('dotenv').config();
const { query } = require('../config/database');
const bcrypt = require('bcryptjs');

async function addSampleData() {
  try {
    console.log('🚀 Adding sample data...');

    // Add sample owners
    const owners = [
      {
        email: '<EMAIL>',
        password: 'Owner123',
        full_name: '<PERSON><PERSON>',
        phone: '+62 812-3456-7890',
        role: 'owner'
      },
      {
        email: '<EMAIL>', 
        password: 'Owner123',
        full_name: '<PERSON><PERSON>',
        phone: '+62 813-4567-8901',
        role: 'owner'
      },
      {
        email: '<EMAIL>',
        password: 'Owner123', 
        full_name: '<PERSON>',
        phone: '+62 814-5678-9012',
        role: 'owner'
      }
    ];

    const ownerIds = [];
    for (const owner of owners) {
      const hashedPassword = await bcrypt.hash(owner.password, 10);
      const result = await query(
        `INSERT INTO users (email, password_hash, full_name, phone, role) 
         VALUES ($1, $2, $3, $4, $5) 
         ON CONFLICT (email) DO UPDATE SET 
         full_name = EXCLUDED.full_name,
         phone = EXCLUDED.phone,
         role = EXCLUDED.role
         RETURNING id`,
        [owner.email, hashedPassword, owner.full_name, owner.phone, owner.role]
      );
      ownerIds.push(result.rows[0].id);
      console.log(`✅ Owner created: ${owner.full_name}`);
    }

    // Add sample properties
    const properties = [
      {
        name: 'Kost Melati Jakarta Pusat',
        description: 'Kost nyaman dan strategis di Jakarta Pusat dengan fasilitas lengkap. Dekat dengan stasiun MRT dan berbagai pusat perbelanjaan.',
        address: 'Jl. Melati No. 123, Menteng, Jakarta Pusat',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        price_min: 1500000,
        price_max: 2500000,
        total_rooms: 20,
        available_rooms: 5,
        property_type: 'kost',
        gender_type: 'mixed',
        facilities: JSON.stringify(['WiFi', 'AC', 'Kamar Mandi Dalam', 'Parkir Motor', 'Laundry', 'Dapur Bersama']),
        images: JSON.stringify(['/images/kost1-1.jpg', '/images/kost1-2.jpg', '/images/kost1-3.jpg']),
        rating: 4.5
      },
      {
        name: 'Kost Mawar Bandung',
        description: 'Kost eksklusif di Bandung dengan pemandangan gunung. Cocok untuk mahasiswa dan pekerja muda.',
        address: 'Jl. Mawar No. 456, Dago, Bandung',
        city: 'Bandung',
        province: 'Jawa Barat',
        price_min: 1200000,
        price_max: 2000000,
        total_rooms: 15,
        available_rooms: 3,
        property_type: 'kost',
        gender_type: 'putra',
        facilities: JSON.stringify(['WiFi', 'AC', 'Kamar Mandi Dalam', 'Parkir Motor', 'Security 24 Jam']),
        images: JSON.stringify(['/images/kost2-1.jpg', '/images/kost2-2.jpg']),
        rating: 4.2
      },
      {
        name: 'Kost Anggrek Yogyakarta',
        description: 'Kost dekat kampus UGM dengan suasana yang tenang dan nyaman untuk belajar.',
        address: 'Jl. Anggrek No. 789, Sleman, Yogyakarta',
        city: 'Yogyakarta',
        province: 'DI Yogyakarta',
        price_min: 800000,
        price_max: 1500000,
        total_rooms: 25,
        available_rooms: 8,
        property_type: 'kost',
        gender_type: 'putri',
        facilities: JSON.stringify(['WiFi', 'Kamar Mandi Dalam', 'Dapur Bersama', 'Ruang Belajar']),
        images: JSON.stringify(['/images/kost3-1.jpg', '/images/kost3-2.jpg', '/images/kost3-3.jpg', '/images/kost3-4.jpg']),
        rating: 4.7
      },
      {
        name: 'Kost Dahlia Surabaya',
        description: 'Kost modern di pusat kota Surabaya dengan akses mudah ke berbagai tempat.',
        address: 'Jl. Dahlia No. 321, Gubeng, Surabaya',
        city: 'Surabaya',
        province: 'Jawa Timur',
        price_min: 1000000,
        price_max: 1800000,
        total_rooms: 18,
        available_rooms: 6,
        property_type: 'kost',
        gender_type: 'mixed',
        facilities: JSON.stringify(['WiFi', 'AC', 'Kamar Mandi Dalam', 'Parkir Motor', 'Laundry', 'CCTV']),
        images: JSON.stringify(['/images/kost4-1.jpg', '/images/kost4-2.jpg']),
        rating: 4.3
      }
    ];

    for (let i = 0; i < properties.length; i++) {
      const property = properties[i];
      const ownerId = ownerIds[i % ownerIds.length];
      
      await query(
        `INSERT INTO properties (name, description, address, city, province, price_min, price_max, 
         total_rooms, available_rooms, property_type, gender_type, facilities, images, owner_id, rating)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`,
        [property.name, property.description, property.address, property.city, property.province,
         property.price_min, property.price_max, property.total_rooms, property.available_rooms,
         property.property_type, property.gender_type, property.facilities, property.images, ownerId, property.rating]
      );
      console.log(`✅ Property created: ${property.name}`);
    }

    console.log('🎉 Sample data added successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
    process.exit(1);
  }
}

addSampleData();
