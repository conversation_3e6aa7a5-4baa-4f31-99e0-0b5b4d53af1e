import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  MapPinIcon,
  HeartIcon,
  ShareIcon,
  PhoneIcon,
  EnvelopeIcon,
  StarIcon,
  HomeIcon,
  UserIcon,
  EyeIcon,
  ScaleIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { kostAPI, favoritesAPI, handleAPIError } from '../lib/api';
import { formatCurrency, formatDate, cn } from '../lib/utils';
import { useAuth } from '../hooks/useAuth';
import { useComparison } from '../hooks/useComparison';

const PropertyDetailPage = () => {
  const { id } = useParams();
  const [property, setProperty] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const { isAuthenticated } = useAuth();
  const { addToComparison, removeFromComparison, isInComparison } = useComparison();

  useEffect(() => {
    const fetchProperty = async () => {
      try {
        setIsLoading(true);
        const response = await kostAPI.getProperty(id);
        
        if (response.success) {
          setProperty(response.data);
        } else {
          setError('Property not found');
        }
      } catch (error) {
        const errorInfo = handleAPIError(error);
        setError(errorInfo.message);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchProperty();
    }
  }, [id]);

  const toggleFavorite = async () => {
    if (!isAuthenticated || !property) return;

    try {
      if (property.is_favorited) {
        await favoritesAPI.removeFavorite(property.id);
      } else {
        await favoritesAPI.addFavorite(property.id);
      }

      setProperty(prev => ({
        ...prev,
        is_favorited: !prev.is_favorited
      }));
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const toggleComparison = () => {
    if (!property) return;

    if (isInComparison(property.id)) {
      const result = removeFromComparison(property.id);
      if (result.success) {
        console.log(result.message);
      }
    } else {
      const result = addToComparison(property);
      if (result.success) {
        console.log(result.message);
      } else {
        alert(result.message);
      }
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: property.name,
          text: `Lihat kost ${property.name} di KostKu`,
          url: window.location.href,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link berhasil disalin!');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container py-8">
          <div className="animate-pulse">
            <div className="h-64 md:h-96 bg-gray-300 rounded-lg mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="h-32 bg-gray-300 rounded"></div>
                <div className="h-12 bg-gray-300 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <HomeIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Kost Tidak Ditemukan</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link to="/search" className="btn btn-primary">
            Kembali ke Pencarian
          </Link>
        </div>
      </div>
    );
  }

  if (!property) return null;

  const images = property.images && property.images.length > 0
    ? property.images.map(url => ({ url, caption: property.name }))
    : [{ url: '/images/placeholder-kost.jpg', caption: property.name }];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link to="/" className="hover:text-primary-600">Beranda</Link>
          <span>/</span>
          <Link to="/search" className="hover:text-primary-600">Cari Kost</Link>
          <span>/</span>
          <span className="text-gray-900">{property.name}</span>
        </nav>

        {/* Image Gallery */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Main Image */}
            <div className="md:col-span-3">
              <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                {images[selectedImageIndex]?.url ? (
                  <img
                    src={images[selectedImageIndex].url}
                    alt={images[selectedImageIndex].caption || property.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <HomeIcon className="w-16 h-16 text-gray-400" />
                  </div>
                )}
              </div>
            </div>

            {/* Thumbnail Images */}
            <div className="space-y-4">
              {images.slice(0, 4).map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={cn(
                    "w-full aspect-square bg-gray-200 rounded-lg overflow-hidden",
                    selectedImageIndex === index && "ring-2 ring-primary-500"
                  )}
                >
                  {image.url ? (
                    <img
                      src={image.url}
                      alt={image.caption || `${property.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <HomeIcon className="w-8 h-8 text-gray-400" />
                    </div>
                  )}
                </button>
              ))}
              
              {images.length > 4 && (
                <div className="w-full aspect-square bg-gray-800 rounded-lg flex items-center justify-center text-white text-sm font-medium">
                  +{images.length - 4} foto
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Header */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {property.name}
                  </h1>
                  <div className="flex items-center text-gray-600 mb-2">
                    <MapPinIcon className="w-5 h-5 mr-2" />
                    <span>{property.address}, {property.city}, {property.province}</span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <EyeIcon className="w-4 h-4 mr-1" />
                      <span>{property.view_count} views</span>
                    </div>
                    <div className="flex items-center">
                      <StarIcon className="w-4 h-4 mr-1 text-yellow-400" />
                      <span>4.5 (12 ulasan)</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {isAuthenticated && (
                    <button
                      onClick={toggleFavorite}
                      className="btn btn-outline p-2"
                    >
                      {property.is_favorited ? (
                        <HeartSolidIcon className="w-5 h-5 text-red-500" />
                      ) : (
                        <HeartIcon className="w-5 h-5" />
                      )}
                    </button>
                  )}
                  
                  <button
                    onClick={handleShare}
                    className="btn btn-outline p-2"
                  >
                    <ShareIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-2xl font-bold text-primary-600">
                  {formatCurrency(property.price_min)}
                  {property.price_min !== property.price_max && (
                    <span className="text-lg font-normal text-gray-500">
                      - {formatCurrency(property.price_max)}
                    </span>
                  )}
                  <span className="text-base font-normal text-gray-500">/bulan</span>
                </div>
                
                <div className="flex space-x-2">
                  <span className="badge badge-primary">{property.gender_type}</span>
                  <span className="badge badge-secondary">{property.property_type}</span>
                  {property.is_featured && (
                    <span className="badge badge-warning">Pilihan</span>
                  )}
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Deskripsi</h2>
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {property.description || 'Tidak ada deskripsi tersedia.'}
                </p>
              </div>
            </div>

            {/* Facilities */}
            {property.facilities && property.facilities.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Fasilitas</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {property.facilities.map((facility, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200">
                      <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                        <span className="text-primary-600 text-sm">✓</span>
                      </div>
                      <span className="text-gray-700 font-medium">{facility.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Rooms (for authenticated users) */}
            {isAuthenticated && property.rooms && property.rooms.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Kamar Tersedia</h2>
                <div className="space-y-4">
                  {property.rooms.map((room) => (
                    <div key={room.id} className="card">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            Kamar {room.room_number} - {room.room_type}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">
                            {room.size_sqm && `${room.size_sqm} m² • `}
                            Maksimal {room.max_occupancy} orang
                          </p>
                          {room.facilities && room.facilities.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {room.facilities.map((facility, index) => (
                                <span key={index} className="badge badge-secondary text-xs">
                                  {facility.name}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-primary-600">
                            {formatCurrency(room.price)}
                          </div>
                          <div className={cn(
                            "text-sm font-medium",
                            room.is_available ? "text-success-600" : "text-error-600"
                          )}>
                            {room.is_available ? 'Tersedia' : 'Tidak Tersedia'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Testimonials */}
            {property.testimonials && property.testimonials.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Ulasan</h2>
                <div className="space-y-4">
                  {property.testimonials.map((testimonial, index) => (
                    <div key={index} className="card">
                      <div className="flex items-start space-x-4">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <UserIcon className="w-5 h-5 text-primary-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium text-gray-900">{testimonial.reviewer_name}</h4>
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <StarIcon
                                  key={i}
                                  className={cn(
                                    "w-4 h-4",
                                    i < testimonial.rating ? "text-yellow-400 fill-current" : "text-gray-300"
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                          <p className="text-gray-700 mb-2">{testimonial.review_text}</p>
                          <div className="text-sm text-gray-500">
                            Tinggal selama {testimonial.stay_duration} • {formatDate(testimonial.created_at)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Limited Access Message */}
            {property.limited_access && (
              <div className="card bg-primary-50 border-primary-200">
                <div className="text-center py-8">
                  <UserIcon className="w-12 h-12 text-primary-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-primary-900 mb-2">
                    Ingin Melihat Detail Lengkap?
                  </h3>
                  <p className="text-primary-700 mb-4">
                    {property.message}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Link to="/login" className="btn btn-primary">
                      Masuk
                    </Link>
                    <Link to="/register" className="btn btn-outline">
                      Daftar Gratis
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            {isAuthenticated && property.owner && (
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Kontak Pemilik</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <UserIcon className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{property.owner.name}</div>
                      <div className="text-sm text-gray-600">Pemilik Kost</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {property.owner.phone && (
                      <a
                        href={`tel:${property.owner.phone}`}
                        className="btn btn-primary w-full flex items-center justify-center space-x-2"
                      >
                        <PhoneIcon className="w-4 h-4" />
                        <span>Hubungi via Telepon</span>
                      </a>
                    )}
                    
                    {property.owner.email && (
                      <a
                        href={`mailto:${property.owner.email}`}
                        className="btn btn-outline w-full flex items-center justify-center space-x-2"
                      >
                        <EnvelopeIcon className="w-4 h-4" />
                        <span>Kirim Email</span>
                      </a>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Property Info */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Properti</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Kamar</span>
                  <span className="font-medium">{property.total_rooms}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kamar Tersedia</span>
                  <span className="font-medium text-success-600">{property.available_rooms}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tipe Gender</span>
                  <span className="font-medium capitalize">{property.gender_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tipe Properti</span>
                  <span className="font-medium capitalize">{property.property_type}</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
              <div className="space-y-3">
                <Link to="/search" className="btn btn-outline w-full">
                  Cari Kost Lain
                </Link>

                {/* Comparison Button */}
                <button
                  onClick={toggleComparison}
                  className={cn(
                    "btn w-full flex items-center justify-center space-x-2",
                    isInComparison(property.id) ? "btn-secondary" : "btn-outline"
                  )}
                >
                  <ScaleIcon className="w-5 h-5" />
                  <span>
                    {isInComparison(property.id) ? 'Hapus dari Perbandingan' : 'Tambah ke Perbandingan'}
                  </span>
                </button>

                {isAuthenticated && (
                  <button
                    onClick={toggleFavorite}
                    className={cn(
                      "btn w-full flex items-center justify-center space-x-2",
                      property.is_favorited ? "btn-secondary" : "btn-outline"
                    )}
                  >
                    <HeartIcon className="w-5 h-5" />
                    <span>
                      {property.is_favorited ? 'Hapus dari Favorit' : 'Tambah ke Favorit'}
                    </span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetailPage;
