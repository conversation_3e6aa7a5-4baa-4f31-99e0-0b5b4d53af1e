const { query, testConnection } = require('../config/database');

const createTables = async () => {
  try {
    console.log('🚀 Starting database initialization...');

    // Test connection first
    const connected = await testConnection();
    if (!connected) {
      throw new Error('Database connection failed');
    }

    // Create users table (skip if exists, as it's already created with UUID)
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'owner', 'admin')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Users table created/verified');

    // Create properties table
    await query(`
      CREATE TABLE IF NOT EXISTS properties (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        address TEXT NOT NULL,
        city VARCHAR(100) NOT NULL,
        province VARCHAR(100) NOT NULL,
        price_min INTEGER NOT NULL,
        price_max INTEGER NOT NULL,
        total_rooms INTEGER DEFAULT 0,
        available_rooms INTEGER DEFAULT 0,
        property_type VARCHAR(50) DEFAULT 'kost',
        gender_type VARCHAR(20) DEFAULT 'mixed',
        facilities JSONB DEFAULT '[]',
        images JSONB DEFAULT '[]',
        owner_id UUID REFERENCES users(id),
        is_active BOOLEAN DEFAULT true,
        views INTEGER DEFAULT 0,
        rating DECIMAL(3,2) DEFAULT 0.0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Properties table created/verified');

    // Create favorites table
    await query(`
      CREATE TABLE IF NOT EXISTS favorites (
        id SERIAL PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, property_id)
      )
    `);
    console.log('✅ Favorites table created/verified');

    // Create reviews table
    await query(`
      CREATE TABLE IF NOT EXISTS reviews (
        id SERIAL PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, property_id)
      )
    `);
    console.log('✅ Reviews table created/verified');

    // Insert sample data if properties table is empty
    const propertyCount = await query('SELECT COUNT(*) FROM properties');
    if (parseInt(propertyCount.rows[0].count) === 0) {
      console.log('📝 Inserting sample properties...');

      // Get existing user UUIDs
      const users = await query('SELECT id, email FROM users ORDER BY created_at LIMIT 4');
      if (users.rows.length < 3) {
        console.log('⚠️ Not enough users found, skipping property insertion');
      } else {
        const [admin, owner1, owner2, owner3] = users.rows;

        // Insert sample properties using existing user UUIDs
        await query(`
          INSERT INTO properties (name, description, address, city, province, price_min, price_max, total_rooms, available_rooms, property_type, gender_type, facilities, images, owner_id, views, rating) VALUES
          ('Kost Melati Indah', 'Kost nyaman dengan fasilitas lengkap di pusat kota Malang. Dekat dengan universitas dan pusat perbelanjaan.', 'Jl. Melati No. 15, Malang', 'Malang', 'Jawa Timur', 800000, 1200000, 20, 15, 'kost', 'mixed', '["WiFi", "Parking", "Security 24/7", "Laundry", "Kitchen"]', '["https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800", "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800", "https://images.unsplash.com/photo-1560185127-6ed189bf02f4?w=800"]', $1, 245, 4.5),
          ('Kost Mawar Residence', 'Kost eksklusif khusus wanita dengan keamanan 24 jam dan fasilitas premium.', 'Jl. Mawar No. 8, Malang', 'Malang', 'Jawa Timur', 1000000, 1500000, 15, 10, 'kost', 'female', '["WiFi", "Parking", "Security 24/7", "Gym", "Swimming Pool"]', '["https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800", "https://images.unsplash.com/photo-1560185127-6ed189bf02f4?w=800", "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800"]', $2, 189, 4.5),
          ('Kost Anggrek Modern', 'Kost modern dengan desain minimalis. Dekat dengan berbagai fasilitas umum.', 'Jl. Anggrek No. 22, Yogyakarta', 'Yogyakarta', 'DI Yogyakarta', 700000, 1000000, 25, 18, 'kost', 'male', '["WiFi", "Parking", "Kitchen", "CCTV"]', '["https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800", "https://images.unsplash.com/photo-1560185127-6ed189bf02f4?w=800", "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800", "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800"]', $3, 156, 4.5)
        `, [owner1.id, owner2.id, owner3.id || owner1.id]);

        console.log('✅ Sample properties inserted successfully');
      }
    }

    console.log('🎉 Database initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
};

// Run the initialization
createTables();
