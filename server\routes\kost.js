const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { validateSearchQuery } = require('../middleware/validation');

const router = express.Router();

// Get all kost properties with search and filtering (public endpoint)
router.get('/', optionalAuth, validateSearchQuery, async (req, res) => {
  try {
    const {
      city,
      price_min,
      price_max,
      gender_type,
      property_type,
      search,
      page = 1,
      limit = 12,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build WHERE clause
    let whereConditions = ['kp.is_active = true'];
    let queryParams = [];
    let paramCount = 0;

    if (city) {
      paramCount++;
      whereConditions.push(`LOWER(kp.city) LIKE LOWER($${paramCount})`);
      queryParams.push(`%${city}%`);
    }

    if (price_min) {
      paramCount++;
      whereConditions.push(`kp.price_min >= $${paramCount}`);
      queryParams.push(parseInt(price_min));
    }

    if (price_max) {
      paramCount++;
      whereConditions.push(`kp.price_max <= $${paramCount}`);
      queryParams.push(parseInt(price_max));
    }

    if (gender_type && gender_type !== 'all') {
      paramCount++;
      whereConditions.push(`kp.gender_type = $${paramCount}`);
      queryParams.push(gender_type);
    }

    if (property_type && property_type !== 'all') {
      paramCount++;
      whereConditions.push(`kp.property_type = $${paramCount}`);
      queryParams.push(property_type);
    }

    if (search) {
      paramCount++;
      whereConditions.push(`(LOWER(kp.name) LIKE LOWER($${paramCount}) OR LOWER(kp.description) LIKE LOWER($${paramCount}) OR LOWER(kp.address) LIKE LOWER($${paramCount}))`);
      queryParams.push(`%${search}%`);
    }

    const whereClause = whereConditions.join(' AND ');

    // Validate sort parameters
    const validSortFields = ['created_at', 'price_min', 'name', 'views'];
    const validSortOrders = ['ASC', 'DESC'];
    
    const sortField = validSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = validSortOrders.includes(sort_order.toUpperCase()) ? sort_order.toUpperCase() : 'DESC';

    // Main query for properties
    const propertiesQuery = `
      SELECT
        kp.id,
        kp.name,
        kp.description,
        kp.address,
        kp.city,
        kp.province,
        kp.price_min,
        kp.price_max,
        kp.total_rooms,
        kp.available_rooms,
        kp.property_type,
        kp.gender_type,
        kp.facilities,
        kp.images,
        kp.rating,
        kp.views,
        kp.created_at,
        u.full_name as owner_name,
        CASE WHEN f.user_id IS NOT NULL THEN true ELSE false END as is_favorited
      FROM properties kp
      LEFT JOIN users u ON kp.owner_id = u.id
      LEFT JOIN favorites f ON kp.id = f.property_id AND f.user_id = $${paramCount + 1}
      WHERE ${whereClause}
      ORDER BY kp.${sortField} ${sortDirection}
      LIMIT $${paramCount + 2} OFFSET $${paramCount + 3}
    `;

    // Add user ID for favorites (null if not authenticated)
    queryParams.push(req.user ? req.user.id : null);
    queryParams.push(parseInt(limit));
    queryParams.push(offset);

    const propertiesResult = await query(propertiesQuery, queryParams);

    // Count total properties for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM properties kp
      WHERE ${whereClause}
    `;

    const countResult = await query(countQuery, queryParams.slice(0, paramCount));
    const total = parseInt(countResult.rows[0].total);

    // Facilities are already included in the properties query as JSONB

    // Format response
    const properties = propertiesResult.rows.map(property => ({
      id: property.id,
      name: property.name,
      description: req.user ? property.description : (property.description ? property.description.substring(0, 150) + '...' : null),
      address: property.address,
      city: property.city,
      province: property.province,
      price_min: property.price_min,
      price_max: property.price_max,
      total_rooms: property.total_rooms,
      available_rooms: property.available_rooms,
      property_type: property.property_type,
      gender_type: property.gender_type,
      facilities: property.facilities || [],
      images: property.images || [],
      rating: property.rating || 0,
      views: property.views || 0,
      owner_name: req.user ? property.owner_name : null, // Hide owner info for non-authenticated users
      is_favorited: property.is_favorited,
      created_at: property.created_at
    }));

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        properties,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        },
        filters: {
          city,
          price_min,
          price_max,
          gender_type,
          property_type,
          search
        }
      }
    });
  } catch (error) {
    console.error('Get properties error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get single kost property details
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const isAuthenticated = !!req.user;

    // Get property details
    const propertyQuery = `
      SELECT 
        kp.*,
        u.full_name as owner_name,
        u.email as owner_email,
        u.phone as owner_phone,
        CASE WHEN f.user_id IS NOT NULL THEN true ELSE false END as is_favorited
      FROM properties kp
      LEFT JOIN users u ON kp.owner_id = u.id
      LEFT JOIN favorites f ON kp.id = f.property_id AND f.user_id = $1
      WHERE kp.id = $2 AND kp.is_active = true
    `;

    const propertyResult = await query(propertyQuery, [req.user ? req.user.id : null, id]);

    if (propertyResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    const property = propertyResult.rows[0];

    // Update view count (only if not the owner)
    if (!req.user || req.user.id !== property.owner_id) {
      await query(
        'UPDATE properties SET views = views + 1 WHERE id = $1',
        [id]
      );
    }

    // Facilities and images are already included in the property data as JSONB
    // For testimonials, we'll use the reviews table
    const reviewsQuery = `
      SELECT
        u.full_name as reviewer_name,
        r.rating,
        r.comment as review_text,
        r.created_at
      FROM reviews r
      LEFT JOIN users u ON r.user_id = u.id
      WHERE r.property_id = $1
      ORDER BY r.created_at DESC
      ${isAuthenticated ? '' : 'LIMIT 2'}
    `;

    const reviewsResult = await query(reviewsQuery, [id]);

    // Room information is included in the property data (total_rooms, available_rooms)

    // Format response based on authentication status
    const responseData = {
      id: property.id,
      name: property.name,
      description: property.description,
      address: property.address,
      city: property.city,
      province: property.province,
      price_min: property.price_min,
      price_max: property.price_max,
      total_rooms: property.total_rooms,
      available_rooms: property.available_rooms,
      property_type: property.property_type,
      gender_type: property.gender_type,
      facilities: property.facilities || [],
      images: property.images || [],
      rating: property.rating || 0,
      views: property.views || 0,
      is_favorited: property.is_favorited,
      reviews: reviewsResult.rows,
      created_at: property.created_at
    };

    // Add detailed info only for authenticated users
    if (isAuthenticated) {
      responseData.owner = {
        name: property.owner_name,
        email: property.owner_email,
        phone: property.owner_phone
      };
    } else {
      responseData.limited_access = true;
      responseData.message = 'Login to see full details, contact information, and room availability';
    }

    res.json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('Get property details error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
