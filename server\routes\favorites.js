const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get user's favorite properties
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 12 } = req.query;
    const offset = (page - 1) * limit;

    const favoritesQuery = `
      SELECT
        kp.id,
        kp.name,
        kp.description,
        kp.address,
        kp.city,
        kp.province,
        kp.price_min,
        kp.price_max,
        kp.total_rooms,
        kp.available_rooms,
        kp.property_type,
        kp.gender_type,
        kp.facilities,
        kp.images,
        kp.rating,
        kp.views,
        f.created_at as favorited_at,
        u.full_name as owner_name
      FROM favorites f
      JOIN properties kp ON f.property_id = kp.id
      LEFT JOIN users u ON kp.owner_id = u.id
      WHERE f.user_id = $1 AND kp.is_active = true
      ORDER BY f.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const favoritesResult = await query(favoritesQuery, [userId, parseInt(limit), offset]);

    // Count total favorites
    const countQuery = `
      SELECT COUNT(*) as total
      FROM favorites f
      JOIN properties kp ON f.property_id = kp.id
      WHERE f.user_id = $1 AND kp.is_active = true
    `;

    const countResult = await query(countQuery, [userId]);
    const total = parseInt(countResult.rows[0].total);

    // Facilities are already included in the properties query as JSONB

    const properties = favoritesResult.rows.map(property => ({
      ...property,
      is_favorited: true,
      facilities: property.facilities || [],
      images: property.images || [],
      rating: property.rating || 0,
      views: property.views || 0
    }));

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        properties,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Add property to favorites
router.post('/:propertyId', authenticateToken, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const userId = req.user.id;

    // Check if property exists and is active
    const propertyCheck = await query(
      'SELECT id FROM properties WHERE id = $1 AND is_active = true',
      [propertyId]
    );

    if (propertyCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    // Check if already favorited
    const existingFavorite = await query(
      'SELECT id FROM favorites WHERE user_id = $1 AND property_id = $2',
      [userId, propertyId]
    );

    if (existingFavorite.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Property already in favorites'
      });
    }

    // Add to favorites
    await query(
      'INSERT INTO favorites (user_id, property_id) VALUES ($1, $2)',
      [userId, propertyId]
    );

    res.status(201).json({
      success: true,
      message: 'Property added to favorites'
    });
  } catch (error) {
    console.error('Add favorite error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Remove property from favorites
router.delete('/:propertyId', authenticateToken, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const userId = req.user.id;

    const result = await query(
      'DELETE FROM favorites WHERE user_id = $1 AND property_id = $2 RETURNING id',
      [userId, propertyId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Favorite not found'
      });
    }

    res.json({
      success: true,
      message: 'Property removed from favorites'
    });
  } catch (error) {
    console.error('Remove favorite error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Check if property is favorited
router.get('/check/:propertyId', authenticateToken, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const userId = req.user.id;

    const result = await query(
      'SELECT id FROM favorites WHERE user_id = $1 AND property_id = $2',
      [userId, propertyId]
    );

    res.json({
      success: true,
      data: {
        is_favorited: result.rows.length > 0
      }
    });
  } catch (error) {
    console.error('Check favorite error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
