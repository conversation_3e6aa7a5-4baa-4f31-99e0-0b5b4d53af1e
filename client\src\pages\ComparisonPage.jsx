import React from 'react';
import { Link } from 'react-router-dom';
import {
  XMarkIcon,
  MapPinIcon,
  HomeIcon,
  UserIcon,
  EyeIcon,
  StarIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { useComparison } from '../hooks/useComparison';
import { formatCurrency } from '../lib/utils';

const ComparisonPage = () => {
  const { comparisonItems, removeFromComparison, clearComparison } = useComparison();

  if (comparisonItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Perbandingan Kost</h1>
            <p className="text-gray-600">Bandingkan kost-kost pilihan Anda</p>
          </div>

          {/* Empty State */}
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-6 flex items-center justify-center">
              <HomeIcon className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Belum Ada Kost untuk Dibandingkan
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Tambahkan kost ke perbandingan dari halaman pencarian atau detail kost untuk melihat perbandingan
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/search" className="btn btn-primary">
                Cari Kost
              </Link>
              <Link to="/" className="btn btn-outline">
                Kembali ke Beranda
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const comparisonFeatures = [
    { key: 'price_range', label: 'Rentang Harga' },
    { key: 'total_rooms', label: 'Total Kamar' },
    { key: 'available_rooms', label: 'Kamar Tersedia' },
    { key: 'property_type', label: 'Tipe Properti' },
    { key: 'gender_type', label: 'Tipe Gender' },
    { key: 'city', label: 'Lokasi' },
    { key: 'view_count', label: 'Dilihat' },
    { key: 'facilities', label: 'Fasilitas' }
  ];

  const getFeatureValue = (property, feature) => {
    switch (feature.key) {
      case 'price_range':
        return `${formatCurrency(property.price_min)} - ${formatCurrency(property.price_max)}`;
      case 'total_rooms':
        return `${property.total_rooms} kamar`;
      case 'available_rooms':
        return `${property.available_rooms} kamar`;
      case 'property_type':
        return property.property_type?.charAt(0).toUpperCase() + property.property_type?.slice(1) || '-';
      case 'gender_type': {
        const genderMap = { male: 'Pria', female: 'Wanita', mixed: 'Campur' };
        return genderMap[property.gender_type] || property.gender_type;
      }
      case 'city':
        return `${property.city}, ${property.province}`;
      case 'view_count':
        return `${property.view_count || 0}x`;
      case 'facilities':
        return property.facilities?.length || 0;
      default:
        return property[feature.key] || '-';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Perbandingan Kost</h1>
            <p className="text-gray-600">
              Membandingkan {comparisonItems.length} kost
            </p>
          </div>
          <button
            onClick={clearComparison}
            className="btn btn-outline text-red-600 border-red-300 hover:bg-red-50"
          >
            Hapus Semua
          </button>
        </div>

        {/* Comparison Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left p-6 font-semibold text-gray-900 bg-gray-50 sticky left-0 z-10">
                    Fitur
                  </th>
                  {comparisonItems.map((property) => (
                    <th key={property.id} className="p-6 min-w-80">
                      <div className="text-center">
                        {/* Property Image */}
                        <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                          {property.main_photo_url ? (
                            <img
                              src={property.main_photo_url}
                              alt={property.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <HomeIcon className="w-12 h-12 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Property Name */}
                        <Link 
                          to={`/property/${property.id}`}
                          className="font-semibold text-gray-900 hover:text-primary-600 transition-colors block mb-2"
                        >
                          {property.name}
                        </Link>

                        {/* Remove Button */}
                        <button
                          onClick={() => removeFromComparison(property.id)}
                          className="text-red-600 hover:text-red-700 text-sm flex items-center justify-center mx-auto"
                        >
                          <XMarkIcon className="w-4 h-4 mr-1" />
                          Hapus
                        </button>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {comparisonFeatures.map((feature, index) => (
                  <tr key={feature.key} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="p-6 font-medium text-gray-900 bg-gray-50 sticky left-0 z-10 border-r border-gray-200">
                      {feature.label}
                    </td>
                    {comparisonItems.map((property) => (
                      <td key={property.id} className="p-6 text-center">
                        <span className="text-gray-900">
                          {getFeatureValue(property, feature)}
                        </span>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/search" className="btn btn-primary">
            Cari Kost Lain
          </Link>
          <Link to="/favorites" className="btn btn-outline">
            Lihat Favorit
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ComparisonPage;
